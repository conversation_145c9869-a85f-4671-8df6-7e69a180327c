#!/usr/bin/env python3
"""
Example Pydantic AI agent demonstrating basic usage.
"""

import os
from dotenv import load_dotenv
from pydantic_ai import Agent
from pydantic import BaseModel

# Load environment variables
load_dotenv()

# Define response model
class WeatherResponse(BaseModel):
    location: str
    temperature: str
    description: str
    recommendation: str

# Create an agent
weather_agent = Agent(
    'openai:gpt-4o-mini',  # You can change this to other models
    result_type=WeatherResponse,
    system_prompt=(
        'You are a helpful weather assistant. '
        'Provide weather information and clothing recommendations.'
    ),
)

async def main():
    """Main function to demonstrate the agent."""
    try:
        # Check if API key is set
        if not os.getenv('OPENAI_API_KEY'):
            print("❌ Please set your OPENAI_API_KEY in a .env file")
            print("Copy .env.example to .env and add your API key")
            return
        
        # Run the agent
        result = await weather_agent.run(
            'What should I wear in San Francisco today?'
        )
        
        print("🤖 Agent Response:")
        print(f"Location: {result.data.location}")
        print(f"Temperature: {result.data.temperature}")
        print(f"Description: {result.data.description}")
        print(f"Recommendation: {result.data.recommendation}")
        
    except Exception as e:
        print(f"❌ Error running agent: {e}")
        print("Make sure you have set up your API keys correctly")

if __name__ == '__main__':
    import asyncio
    asyncio.run(main())
