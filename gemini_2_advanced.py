#!/usr/bin/env python3
"""
Advanced examples showcasing Gemini 2.0 Flash capabilities with Pydantic AI.
"""

import os
import asyncio
from dotenv import load_dotenv
from pydantic_ai import Agent
from pydantic import BaseModel
from typing import List, Optional

# Load environment variables
load_dotenv()

# Advanced response models
class TechnicalAnalysis(BaseModel):
    technology: str
    pros: List[str]
    cons: List[str]
    use_cases: List[str]
    complexity_score: int  # 1-10
    learning_curve: str
    market_demand: str
    recommendation: str

class ProjectPlan(BaseModel):
    project_name: str
    description: str
    phases: List[str]
    estimated_duration: str
    required_skills: List[str]
    potential_challenges: List[str]
    success_metrics: List[str]
    budget_estimate: Optional[str] = None

class DataInsight(BaseModel):
    insight_type: str
    key_finding: str
    supporting_evidence: List[str]
    implications: List[str]
    confidence_level: int  # 1-100
    recommended_actions: List[str]

# Create specialized agents
tech_analyst = Agent(
    'gemini-2.0-flash-exp',
    result_type=TechnicalAnalysis,
    system_prompt=(
        'You are a senior technology analyst with deep expertise in software development, '
        'emerging technologies, and market trends. Provide comprehensive, balanced analysis '
        'with practical insights and actionable recommendations.'
    ),
)

project_planner = Agent(
    'gemini-2.0-flash-exp',
    result_type=ProjectPlan,
    system_prompt=(
        'You are an experienced project manager and technical architect. '
        'Create detailed, realistic project plans with clear phases, timelines, '
        'and risk assessments. Focus on practical implementation strategies.'
    ),
)

data_analyst = Agent(
    'gemini-2.0-flash-exp',
    result_type=DataInsight,
    system_prompt=(
        'You are a data scientist and business intelligence expert. '
        'Analyze information to extract meaningful insights, identify patterns, '
        'and provide data-driven recommendations for decision making.'
    ),
)

async def analyze_technology():
    """Demonstrate advanced technology analysis."""
    print("🔬 Advanced Technology Analysis with Gemini 2.0 Flash...")
    
    try:
        result = await tech_analyst.run(
            "Analyze the current state and future prospects of WebAssembly (WASM) "
            "for web development, including its impact on JavaScript and performance benefits."
        )
        
        print(f"Technology: {result.data.technology}")
        print(f"Complexity Score: {result.data.complexity_score}/10")
        print(f"Learning Curve: {result.data.learning_curve}")
        print(f"Market Demand: {result.data.market_demand}")
        
        print("\nPros:")
        for pro in result.data.pros:
            print(f"  ✅ {pro}")
        
        print("\nCons:")
        for con in result.data.cons:
            print(f"  ❌ {con}")
        
        print("\nUse Cases:")
        for use_case in result.data.use_cases:
            print(f"  🎯 {use_case}")
        
        print(f"\nRecommendation: {result.data.recommendation}")
        print("-" * 80)
        
    except Exception as e:
        print(f"❌ Error in technology analysis: {e}")

async def create_project_plan():
    """Demonstrate advanced project planning."""
    print("📋 Advanced Project Planning with Gemini 2.0 Flash...")
    
    try:
        result = await project_planner.run(
            "Create a comprehensive project plan for building a real-time collaborative "
            "code editor (like VS Code Live Share) using modern web technologies. "
            "Include technical architecture, development phases, and team requirements."
        )
        
        print(f"Project: {result.data.project_name}")
        print(f"Description: {result.data.description}")
        print(f"Duration: {result.data.estimated_duration}")
        
        print("\nDevelopment Phases:")
        for i, phase in enumerate(result.data.phases, 1):
            print(f"  {i}. {phase}")
        
        print("\nRequired Skills:")
        for skill in result.data.required_skills:
            print(f"  🛠️ {skill}")
        
        print("\nPotential Challenges:")
        for challenge in result.data.potential_challenges:
            print(f"  ⚠️ {challenge}")
        
        print("\nSuccess Metrics:")
        for metric in result.data.success_metrics:
            print(f"  📊 {metric}")
        
        if result.data.budget_estimate:
            print(f"\nBudget Estimate: {result.data.budget_estimate}")
        
        print("-" * 80)
        
    except Exception as e:
        print(f"❌ Error in project planning: {e}")

async def analyze_data_trends():
    """Demonstrate advanced data analysis capabilities."""
    print("📈 Advanced Data Analysis with Gemini 2.0 Flash...")
    
    try:
        result = await data_analyst.run(
            "Analyze the trend of remote work adoption in the tech industry post-2020. "
            "Consider factors like productivity metrics, employee satisfaction, "
            "company cost savings, and long-term implications for the industry."
        )
        
        print(f"Insight Type: {result.data.insight_type}")
        print(f"Key Finding: {result.data.key_finding}")
        print(f"Confidence Level: {result.data.confidence_level}%")
        
        print("\nSupporting Evidence:")
        for evidence in result.data.supporting_evidence:
            print(f"  📋 {evidence}")
        
        print("\nImplications:")
        for implication in result.data.implications:
            print(f"  💡 {implication}")
        
        print("\nRecommended Actions:")
        for action in result.data.recommended_actions:
            print(f"  🎯 {action}")
        
        print("-" * 80)
        
    except Exception as e:
        print(f"❌ Error in data analysis: {e}")

async def main():
    """Main function to run all advanced examples."""
    # Check if API key is set
    if not os.getenv('GOOGLE_AI_API_KEY') or os.getenv('GOOGLE_AI_API_KEY') == 'your_google_ai_api_key_here':
        print("❌ Please set your GOOGLE_AI_API_KEY in the .env file")
        return
    
    print("🚀 Advanced Gemini 2.0 Flash Examples with Pydantic AI\n")
    print("This demo showcases the enhanced reasoning and analysis capabilities")
    print("of the latest Gemini 2.0 Flash model.\n")
    
    # Run all advanced examples
    await analyze_technology()
    await create_project_plan()
    await analyze_data_trends()
    
    print("✅ All advanced examples completed!")
    print("\n🎉 Gemini 2.0 Flash demonstrates superior reasoning, structured output,")
    print("and comprehensive analysis capabilities!")

if __name__ == '__main__':
    asyncio.run(main())
