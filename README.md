# Pydantic AI Project

This project is set up with Pydantic AI and all necessary prerequisites.

## Setup Complete ✅

- Python 3.12.2 detected
- Virtual environment created (`venv/`)
- Pydantic AI v0.4.11 installed with all dependencies
- Requirements file generated

## Project Structure

```
BMADPydanticAgents/
├── venv/                 # Virtual environment
├── test_pydantic_ai.py   # Installation test script
├── example_agent.py      # Example weather agent
├── .env.example          # API key template
├── requirements.txt      # Python dependencies
└── README.md            # This file
```

## Getting Started

### 1. Activate Virtual Environment

```bash
# Windows
venv\Scripts\activate

# Linux/Mac
source venv/bin/activate
```

### 2. Set Up API Keys

1. Copy the environment template:
   ```bash
   copy .env.example .env
   ```

2. Edit `.env` and add your API keys for the AI providers you want to use:
   - OpenAI (GPT models)
   - Anthropic (Claude models)
   - Google AI (Gemini models)
   - Groq, Mistral, Cohere, etc.

### 3. Test Installation

```bash
python test_pydantic_ai.py
```

### 4. Run Example Agent

```bash
python example_agent.py
```

## Supported AI Providers

Pydantic AI supports multiple AI providers:

- **OpenAI**: GPT-4, GPT-3.5, etc.
- **Anthropic**: Claude 3.5 Sonnet, Claude 3 Haiku, etc.
- **Google**: Gemini Pro, Gemini Flash
- **Groq**: Fast inference for Llama, Mixtral models
- **Mistral AI**: Mistral models
- **Cohere**: Command models
- **AWS Bedrock**: Various models through AWS

## Next Steps

1. Set up your preferred AI provider API keys
2. Explore the example agent
3. Create your own agents for specific tasks
4. Build AI-powered applications!

## Resources

- [Pydantic AI Documentation](https://ai.pydantic.dev/)
- [Pydantic AI GitHub](https://github.com/pydantic/pydantic-ai)
- [API Provider Setup Guides](https://ai.pydantic.dev/install/)
