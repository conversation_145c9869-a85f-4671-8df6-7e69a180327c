#!/usr/bin/env python3
"""
Test script to verify Pydantic AI installation and basic functionality.
"""

try:
    import pydantic_ai
    print(f"✅ Pydantic AI successfully imported! Version: {pydantic_ai.__version__}")
    
    # Test basic imports
    from pydantic_ai import Agent
    from pydantic import BaseModel
    print("✅ Core components imported successfully!")
    
    # Create a simple agent (without API key for now)
    class SimpleResponse(BaseModel):
        message: str
        
    print("✅ Pydantic AI is ready to use!")
    print("\nNext steps:")
    print("1. Set up your AI provider API keys (OpenAI, Anthropic, etc.)")
    print("2. Create your first agent")
    print("3. Start building AI-powered applications!")
    
except ImportError as e:
    print(f"❌ Error importing Pydantic AI: {e}")
except Exception as e:
    print(f"❌ Unexpected error: {e}")
